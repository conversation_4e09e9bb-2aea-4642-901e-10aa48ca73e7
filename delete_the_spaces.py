import pandas as pd
from openpyxl.styles import Font

def clean_cell_content(value):
    """清理单元格内容，删除空格和换行符"""
    if isinstance(value, str):
        return value.replace(' ', '').replace('\n', '').replace('\r', '')
    return value

def process_excel_file(file_path):
    """处理Excel文件，清理所有单元格的内容"""
    try:
        # 读取所有工作表
        excel_file = pd.ExcelFile(file_path)
        
        # 创建ExcelWriter对象用于保存结果
        output_file = file_path.replace('.xlsx', '_cleaned.xlsx')
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 处理每个工作表
            for sheet_name in excel_file.sheet_names:
                print(f'正在处理工作表: {sheet_name}')
                
                # 读取工作表数据
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                # 对所有单元格应用清理函数
                df = df.map(clean_cell_content)
                
                # 保存处理后的工作表
                df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 设置所有单元格字体为Arial
            workbook = writer.book
            arial_font = Font(name='Arial')
            
            for worksheet in workbook.worksheets:
                for row in worksheet.rows:
                    for cell in row:
                        cell.font = arial_font
        
        print(f'处理完成！已保存到: {output_file}')
        
    except Exception as e:
        print(f'处理文件时出错: {str(e)}')

def main():
    file_path = input('请输入Excel文件路径: ')
    if not file_path.endswith(('.xlsx', '.xlsm')):
        print('错误：请输入有效的Excel文件（.xlsx或.xlsm）')
        return
    process_excel_file(file_path)

if __name__ == '__main__':
    main()