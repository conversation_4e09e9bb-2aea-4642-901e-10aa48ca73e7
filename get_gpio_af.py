import pandas as pd
import os

def load_gpio_data(file_path):
    """
    读取GPIO AF Excel文件并转换为便于查询的数据结构
    """
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return None
    
    try:
        # 读取Excel文件，跳过第一行（标题行）
        df = pd.read_excel(file_path, header=0)
        
        # 获取AF名称（第二行，从第二列开始）
        af_names = df.columns.tolist()[1:]
        
        # 创建三个字典用于不同条件的查询
        # 1. 通过引脚名和用途查找AF名称: {(pin_name, usage): af_name}
        pin_usage_to_af = {}
        # 2. 通过引脚名和AF名称查找用途: {(pin_name, af_name): usage}
        pin_af_to_usage = {}
        # 3. 通过AF名称和用途查找引脚名: {(af_name, usage): [pin_names]}
        af_usage_to_pins = {}
        
        # 遍历数据行
        for _, row in df.iterrows():
            pin_name = row.iloc[0]  # 第一列是引脚名称
            
            # 遍历每个AF列
            for i, af_name in enumerate(af_names):
                usage = row.iloc[i+1]  # AF对应的用途
                
                # 跳过空值
                if pd.isna(usage) or usage == "":
                    continue
                
                # 填充查询字典
                pin_usage_to_af[(pin_name, usage)] = af_name
                pin_af_to_usage[(pin_name, af_name)] = usage
                
                # 对于AF名称和用途到引脚名的映射，可能有多个引脚
                if (af_name, usage) not in af_usage_to_pins:
                    af_usage_to_pins[(af_name, usage)] = []
                af_usage_to_pins[(af_name, usage)].append(pin_name)
        
        return {
            "pin_usage_to_af": pin_usage_to_af,
            "pin_af_to_usage": pin_af_to_usage,
            "af_usage_to_pins": af_usage_to_pins,
            "af_names": af_names
        }
    
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def query_gpio_data(data, **kwargs):
    """
    根据提供的条件查询GPIO数据
    
    参数:
        data: 由load_gpio_data函数返回的数据结构
        **kwargs: 可以包含以下键值对:
            - pin_name: 引脚名称
            - af_name: AF名称
            - usage: 用途
    
    返回:
        根据提供的条件返回相应的查询结果
    """
    if data is None:
        return "数据未加载"
    
    pin_name = kwargs.get("pin_name")
    af_name = kwargs.get("af_name")
    usage = kwargs.get("usage")
    
    # 检查提供的条件数量
    conditions = sum(1 for x in [pin_name, af_name, usage] if x is not None)
    if conditions != 2:
        return "请提供恰好两个条件（引脚名、AF名称或用途）"
    
    # 根据不同的条件组合进行查询
    if pin_name and usage:
        # 通过引脚名和用途查找AF名称
        key = (pin_name, usage)
        if key in data["pin_usage_to_af"]:
            return {"af_name": data["pin_usage_to_af"][key]}
        else:
            return "未找到匹配的AF名称"
    
    elif pin_name and af_name:
        # 通过引脚名和AF名称查找用途
        key = (pin_name, af_name)
        if key in data["pin_af_to_usage"]:
            return {"usage": data["pin_af_to_usage"][key]}
        else:
            return "未找到匹配的用途"
    
    elif af_name and usage:
        # 通过AF名称和用途查找引脚名
        key = (af_name, usage)
        if key in data["af_usage_to_pins"]:
            return {"pin_names": data["af_usage_to_pins"][key]}
        else:
            return "未找到匹配的引脚名"
    
    return "查询条件无效"

# 示例用法
if __name__ == "__main__":
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "Q800_GPIO_AF.xlsx")
    
    # 加载数据
    gpio_data = load_gpio_data(file_path)
    
    if gpio_data:
        print("数据加载成功！")
        print(f"共有 {len(gpio_data['af_names'])} 个AF名称")
        
        # 示例查询
        print("\n示例查询:")
        
        # 假设我们要查询PA0引脚和ADC_IN0用途对应的AF名称
        result1 = query_gpio_data(gpio_data, pin_name="PA0", usage="TIMER1_CH0")
        print(f"PA0引脚和EVENTOUT用途对应的AF名称: {result1}")
        
        # 假设我们要查询PA0引脚和AF1对应的用途
        result2 = query_gpio_data(gpio_data, pin_name="PA0", af_name="AF1")
        print(f"PA0引脚和AF1对应的用途: {result2}")
        
        # 假设我们要查询AF1和UART4_TX用途对应的引脚名
        result3 = query_gpio_data(gpio_data, af_name="AF1", usage="TIMER1_CH1")
        print(f"AF1和TIMER1_CH1用途对应的引脚名: {result3}")
    else:
        print("数据加载失败！")