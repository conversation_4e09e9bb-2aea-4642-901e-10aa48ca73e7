# GPIO AF查询工具

这是一个用于查询GD32微控制器GPIO引脚AF（Alternate Function）配置的Python工具。

## 功能特点

- 支持根据引脚名称和功能名称查询对应的AF编号
- 支持查询特定引脚的所有AF配置
- 支持查询特定功能的所有引脚配置
- 支持查询特定AF的所有配置
- 支持部分匹配查询
- 提供多种查询接口，满足不同使用场景

## 文件说明

- `get_gpio_af.py` - 主要的查询工具模块
- `example_usage.py` - 使用示例
- `Q800_GPIO_AF.xlsx` - GPIO AF配置数据文件
- `README.md` - 本说明文件

## 安装依赖

```bash
pip install pandas openpyxl
```

## 使用方法

### 1. 基本导入

```python
from get_gpio_af import load_gpio_data, find_af_by_pin_and_function, flexible_query

# 加载数据
gpio_data = load_gpio_data("Q800_GPIO_AF.xlsx")
```

### 2. 根据引脚和功能查询AF（主要用途）

```python
# 查询PA0引脚的TIMER1_CH0功能对应的AF编号
result = find_af_by_pin_and_function(gpio_data, "PA0", "TIMER1_CH0")
print(result)
# 输出: {'pin_name': 'PA0', 'af_name': 'AF1', 'usage': 'TIMER1_CH0/TIMER1_ETI'}
```

### 3. 查询特定引脚的所有AF配置

```python
# 查询PA0引脚的所有AF配置
results = flexible_query(gpio_data, pin_name="PA0")
for result in results:
    print(f"{result['af_name']} -> {result['usage']}")
```

### 4. 查询特定功能的所有引脚配置

```python
# 查询所有包含TIMER1的配置
results = flexible_query(gpio_data, usage="TIMER1")
for result in results:
    print(f"{result['pin_name']} {result['af_name']} -> {result['usage']}")
```

### 5. 查询特定AF的所有配置

```python
# 查询AF1的所有配置
results = flexible_query(gpio_data, af_name="AF1")
for result in results:
    print(f"{result['pin_name']} -> {result['usage']}")
```

## 查询函数说明

### `find_af_by_pin_and_function(data, pin_name, function_name)`

**最常用的查询函数**，根据引脚名称和功能名称查询AF编号。

- `pin_name`: 引脚名称，如 "PA0"
- `function_name`: 功能名称，如 "TIMER1_CH0"（支持部分匹配）
- 返回: 匹配的AF信息字典

### `flexible_query(data, **kwargs)`

**灵活查询函数**，支持单个或多个条件的查询。

参数:
- `pin_name`: 引脚名称（可选）
- `af_name`: AF名称（可选）
- `usage`: 用途/功能名称（可选，支持部分匹配）

### `query_gpio_data(data, **kwargs)`

**传统查询函数**，需要恰好提供两个条件。

## 使用示例

运行示例程序：

```bash
python example_usage.py
```

或者运行主程序查看所有功能演示：

```bash
python get_gpio_af.py
```

## 典型使用场景

1. **根据引脚和功能查询AF**（您的主要需求）
   ```python
   result = find_af_by_pin_and_function(gpio_data, "PA0", "TIMER1_CH0")
   # 结果: PA0引脚的TIMER1_CH0功能对应AF1
   ```

2. **查看引脚的所有可用功能**
   ```python
   results = flexible_query(gpio_data, pin_name="PA0")
   # 显示PA0引脚的所有AF配置
   ```

3. **查找特定功能的所有可用引脚**
   ```python
   results = flexible_query(gpio_data, usage="USART1_TX")
   # 显示所有支持USART1_TX功能的引脚
   ```

## 注意事项

- 功能名称查询支持部分匹配，例如查询"TIMER1"会匹配所有包含"TIMER1"的功能
- 引脚名称和AF名称需要精确匹配
- 如果查询结果有多个匹配项，会返回所有匹配的结果

## 数据格式

Excel文件应包含以下列：
- 第一列：PinName（引脚名称）
- 其他列：AF0, AF1, AF2, ... AF15（各AF对应的功能）
